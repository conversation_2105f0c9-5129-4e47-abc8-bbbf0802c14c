<?xml version="1.0" encoding="utf-8"?>
<component size="134,93" extention="Button">
  <controller name="button" pages="0,up,1,down,2,over,3,selectedOver" selected="0"/>
  <displayList>
    <graph id="n0_n4bd" name="n0" xy="0,0" size="134,93" touchable="false" type="rect" lineSize="0" fillColor="#fff0f0f0">
      <gearDisplay controller="button" pages="0"/>
      <relation target="" sidePair="width-width,height-height"/>
    </graph>
    <graph id="n1_n4bd" name="n1" xy="0,0" size="134,93" touchable="false" type="rect" lineSize="0" fillColor="#fffafafa">
      <gearDisplay controller="button" pages="2"/>
      <relation target="" sidePair="width-width,height-height"/>
    </graph>
    <graph id="n2_n4bd" name="n2" xy="0,0" size="134,93" touchable="false" type="rect" lineSize="0" fillColor="#ffcccccc">
      <gearDisplay controller="button" pages="1,3"/>
      <relation target="" sidePair="width-width,height-height"/>
    </graph>
    <loader id="n3_n4bd" name="icon" xy="0,0" size="134,93" align="center" vAlign="middle">
      <relation target="" sidePair="width-width,height-height"/>
    </loader>
  </displayList>
  <Button downEffect="dark" downEffectValue="0.80"/>
</component>