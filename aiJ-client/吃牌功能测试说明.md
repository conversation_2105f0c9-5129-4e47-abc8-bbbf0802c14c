# 吃牌功能测试说明

## 功能概述

本次更新完善了客户端的吃牌流程代码，包括：
1. 接收吃牌通知
2. 显示吃牌按钮
3. 处理吃牌点击事件
4. 显示吃牌组合结果

## 修改的文件

### 1. MahjongGameLayer.ts
- 修改 `renderOperateNotify` 方法，添加吃牌按钮支持
- 修改各方向的牌组合渲染逻辑，支持吃牌组合显示

### 2. MahjongGameEngine.ts
- 修改 `gameOperateNotifyCb` 方法，添加吃牌通知处理
- 修改 `gameOperateResultCb` 方法，添加吃牌结果处理
- 修改 `gamePlayingSceneCb` 方法，支持场景恢复时的吃牌显示

## 实现细节

### 操作枚举值
```typescript
// 服务端定义的操作常量
N = 0  // 过
P = 1  // 碰
G = 2  // 杠
H = 4  // 胡
C = 8  // 吃
```

### 吃牌检测
```typescript
// 在 gameOperateNotifyCb 中检测吃牌
(resp.action & 8) != 0  // 检查是否可以吃牌
```

### 吃牌按钮创建
```typescript
if (chi) {
    let button = fgui.UIPackage.createObject("mahjong", "ChiButton").asCom;
    button.on(fgui.Event.CLICK, (evt: fgui.Event) => {
        let target = fgui.GObject.cast(evt.currentTarget);
        // 发送吃牌操作，action = 8
        AiJKit.use(AppConfig.GAME_WS_NAME).send(new MahjongOperateEvent(8, target.data));
    }, this);
}
```

### 吃牌组合显示
```typescript
case MahjongWeaveType.C:  // 吃牌 - 显示顺子
    let cardValue = weaveItem.centerCard & 0x0F;
    let cardColor = weaveItem.centerCard & 0xF0;
    let card1 = cardColor | (cardValue - 1);
    let card2 = weaveItem.centerCard;
    let card3 = cardColor | (cardValue + 1);
    // 显示三张连续的牌
```

## 测试步骤

### 1. 环境准备
- 确保服务端支持吃牌功能
- 确保UI资源中包含 `ChiButton` 组件
- 确保UI资源中包含各方向的吃牌组合组件：
  - `SouthChiComponent`
  - `NorthChiComponent`
  - `EastChiComponent`
  - `WestChiComponent`

### 2. 功能测试
1. **吃牌通知测试**
   - 当其他玩家出牌时，如果当前玩家可以吃牌，应该显示吃牌按钮
   - 检查按钮是否正确显示在操作区域

2. **吃牌操作测试**
   - 点击吃牌按钮
   - 检查是否发送了正确的操作事件（action=8）
   - 检查服务器是否正确处理吃牌请求

3. **吃牌结果显示测试**
   - 吃牌成功后，检查是否正确显示吃牌组合
   - 检查手牌是否正确更新
   - 检查出牌区是否正确移除被吃的牌

### 3. 边界情况测试
1. **只有下家可以吃牌**
   - 验证只有下家玩家会收到吃牌通知
   - 其他位置的玩家不应该看到吃牌按钮

2. **吃牌优先级**
   - 当同时可以吃、碰、杠时，检查按钮显示顺序
   - 验证操作的优先级处理

3. **场景恢复**
   - 断线重连后，如果当前状态允许吃牌，应该正确显示吃牌按钮

## 注意事项

### 1. UI组件依赖
- 需要确保UI资源中存在相应的吃牌组件
- 如果组件不存在，会导致运行时错误

### 2. 简化实现
- 当前实现使用默认的吃牌组合（中心牌±1）
- 实际游戏中可能需要支持多种吃牌组合选择

### 3. 服务端配合
- 需要服务端正确发送吃牌通知（action包含8）
- 需要服务端正确处理吃牌操作请求
- 需要服务端返回正确的吃牌结果

## 可能的问题

### 1. UI组件缺失
如果出现以下错误：
```
Cannot create object 'ChiButton' from package 'mahjong'
```
说明UI资源中缺少吃牌按钮组件，需要在FairyGUI中添加。

### 2. 组合显示错误
如果吃牌组合显示不正确，检查：
- 牌值计算逻辑是否正确
- UI组件的子元素命名是否正确（n0, n1, n2）

### 3. 事件处理问题
如果点击吃牌按钮没有反应，检查：
- 事件注册是否正确
- WebSocket连接是否正常
- 服务端是否正确处理请求

## 后续优化

1. **多组合选择**
   - 支持玩家选择不同的吃牌组合
   - 添加组合选择界面

2. **动画效果**
   - 添加吃牌操作的动画效果
   - 优化UI交互体验

3. **音效支持**
   - 添加吃牌操作的音效
   - 提升游戏体验
